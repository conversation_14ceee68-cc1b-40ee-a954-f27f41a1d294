#!/bin/bash
# NOTE: server need to install jq
# TODO: use config alertEmail
# usage: ./testPassed.sh -e <EMAIL>

# exit on error
set -e

PATH="$PATH:/usr/local/bin:/usr/bin:/usr/local/sbin:/usr/sbin"
RM_FROM=<EMAIL>
RM_URL=https://ml1.realmaster.cc/send
rmText=''

usage()
{
  echo ""
  echo "Usage: $0 -e notify email, edit passedFiles.csv and passedModules.csv for which tests to run, -d /usr/cfgdir"  
  echo -e "\t-e email address eg. <EMAIL>,<EMAIL>"
  echo -e "\t-d cfg dir location"
  exit 1 # Exit script after printing help
}


while getopts "e:d:" option; do
  case "${option}" in
    e)
        RM_TO=${OPTARG}
        ;;
    d)
        CFG_DIR=${OPTARG}
        ;;
    *)
        usage
        ;;
  esac
done
echo "CFG_DIR: $CFG_DIR"

cd $CFG_DIR

# Check if NODE_PATH is set, if not, find it using 'which node'
if [ -z "$NODE_PATH" ]; then
  NODE_PATH=$(which node)
  echo "NODE_PATH not set. Using: $NODE_PATH"
fi

# Check if COFFEE_PATH is set, if not, find it using 'which coffee'
if [ -z "$COFFEE_PATH" ]; then
  COFFEE_PATH=$(which coffee)
  echo "COFFEE_PATH not set. Using: $COFFEE_PATH"
fi

# Check if CFG_PATH is set, if not, use the default value
if [ -z "$CFG_PATH" ]; then
  CFG_PATH=$CFG_DIR/built/rmIni/unit_test.ini
  echo "CFG_PATH not set. Using: $CFG_PATH"
fi


SRC_DIR=$($NODE_PATH $COFFEE_PATH $CFG_DIR/bin/getValueFromToml.coffee "$CFG_PATH" "serverBase.srcPath")
if [ -z "$SRC_DIR" ]; then # If SRC_DIR is empty, set it to the default value
  SRC_DIR=$(realpath "$CFG_DIR/../appweb/src")
fi
APPWEB_DIR=$(dirname "$SRC_DIR")
echo "SRC_DIR: $SRC_DIR"
echo "APPWEB_DIR: $APPWEB_DIR"
echo "$NODE_PATH $COFFEE_PATH $CFG_DIR/config.coffee"
$NODE_PATH $COFFEE_PATH $CFG_DIR/config.coffee -e $RM_TO -s $SRC_DIR

# Get IS_ELASTIC value from config file

ENGINE=$($NODE_PATH $COFFEE_PATH $CFG_DIR/bin/getValueFromToml.coffee "$CFG_PATH" "propertiesBase.useSearchEngine")
if [ -z "$ENGINE" ]; then # If ENGINE is empty, set it to the default value "Mongo"
  ENGINE="Mongo"
fi

if [[ $ENGINE == "ES" ]]; then
  IS_ELASTIC=true
else
  IS_ELASTIC=false
fi
echo IS_ELASTIC: "$IS_ELASTIC"



# ./mate4.sh compile
rm -rf $SRC_DIR/built
rm -rf $SRC_DIR/unitTestJS
echo "======================= compile start ======================="
echo $SRC_DIR/lib/coffeemate4.coffee $CFG_PATH compile
$NODE_PATH $COFFEE_PATH $SRC_DIR/lib/coffeemate4.coffee $CFG_PATH compile

echo "======================= test start ======================="
CFG_DIR=$PWD
if [ "$IS_ELASTIC" == true ]; then
  reportOutputDir="$CFG_DIR/reports/unitTestES"
else
  reportOutputDir="$CFG_DIR/reports/unitTest"
fi
echo -e "IS_ELASTIC \t= $IS_ELASTIC;\nReport output dir \t=$reportOutputDir"

cp $APPWEB_DIR/docs/Dev_unitTest/tests/passedFiles.csv ./passedFiles.csv
testParam="-c passedFiles.csv"
if [ $IS_ELASTIC == true ]; then
  testParam="-e $testParam"
  # echo "IS_ELASTIC"
fi
echo "testParam: $testParam"
./unitTest/test.sh $testParam
EXIT_STATUS=$?
if [ $EXIT_STATUS -eq 0 ]; then
  echo "✅test passedFiles success."
else
  echo "❌test passedFiles failed!"
  rmText="$rmText <p>test passedFiles failed!</p>"
fi
#if file exits, check failCount, else ignore
if [[ -f $reportOutputDir/mochaReportsFile.json ]]; then
  failCount=$(jq .stats.failures $reportOutputDir/mochaReportsFile.json)
else
  echo "No mochaReportsFile, could be ignored due to passed file not running in ES:  $reportOutputDir/mochaReportsFile.json"
  failCount=0
fi
if [ $failCount -gt 0 ]; then
  rmText="$rmText <p>test failed: $failCount, admin need to manually run test and resolve</p>"
  echo "file test failed: $failCount, admin need to manually run test and resolve!!"
fi

# exit 0
echo "======================= test passed modules ======================="
cp $APPWEB_DIR/docs/Dev_unitTest/tests/passedModules.csv ./passedModules.csv
testParam="-v passedModules.csv"
if [ $IS_ELASTIC == true ]; then
  testParam="-e $testParam"
fi
echo "testModule Param: $testParam"
./unitTest/test.sh $testParam
EXIT_STATUS=$?
if [ $EXIT_STATUS -eq 0 ]; then
  echo "✅test passedModules success."
  # TODO: check json-summary file
else
  echo "❌test passedModules failed!"
  rmText="$rmText <p>test passedModules failed!</p>"
fi


failCount=$(jq .stats.failures $reportOutputDir/mochaReportsModule.json)
if [ $failCount -gt 0 ]; then
  rmText="$rmText <p>test failed: $failCount, admin need to manually run test and resolve</p>"
  echo "module test failed: $failCount, admin need to manually run test and resolve!!"
fi
echo "======================= test end ======================="




echo "rmText: $rmText"
echo "RM_TO: $RM_TO"

if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= Send email ======================="
  rmText="$rmText `jq .stats $reportOutputDir/mochaReportsModule.json`"
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "Auto UnitTest failed" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  # split .to to array by ','
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}" )
  curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$mail"\
    $RM_URL
fi
exit 0
