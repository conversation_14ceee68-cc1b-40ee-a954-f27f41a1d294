#!/bin/bash
# ./unitTest/compile.sh -s [/home/<USER>/appweb/src]
while getopts "s:" o; do
    # echo "o \t= $o"
    case "${o}" in
        s)
            SRC_DIR=${OPTARG}
            ;;
        *)
            usage
            ;;
    esac
done
# SRC_DIR
CFG_DIR=`pwd`
#CFG_PATH=$CFG_PATH+'/local.test.ini'

echo "src = $SRC_DIR"

NODE_PATH=$(which node)
COFFEE_PATH=$(which coffee)

# TODO: fix this
CFG_PATH="$CFG_DIR/built/rmIni/unit_test.ini"
echo "cfg = $CFG_PATH"

echo "$NODE_PATH $COFFEE_PATH $SRC_DIR/lib/coffeemate4.coffee $CFG_PATH compile"
$NODE_PATH $COFFEE_PATH $SRC_DIR/lib/coffeemate4.coffee $CFG_PATH compile