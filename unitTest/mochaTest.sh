#!/bin/bash
# If your script is executable and you are executing it like ./getconfig.sh, the first line of your script needs to be:
# ```!/bin/bash```
# Without that shebang line, your script will be interpreted by sh which doesn't understand [[ in if statements.

# exit on error
set -e

echo ========================================
echo run mochaTest
echo -e "SRC_DIR:\t $SRC_DIR"
echo -e "CFG_DIR:\t $CFG_DIR"
echo -e "report:\t $report"
echo -e "module:\t $module"
echo -e "file:\t $file"
echo -e "fcsv:\t $fcsv"
echo -e "mcsv:\t $mcsv"
rm -r $SRC_DIR/unitTestJs
rm -r $SRC_DIR/coverage
mkdir $SRC_DIR/unitTestJs
mkdir $SRC_DIR/coverage
cd $SRC_DIR
# if [ -e $SRC_DIR/unitTest/$module/_initData.coffee ]; then
#   echo "run $module _initData.coffee"
#   coffee $SRC_DIR/lib/batchBase.coffee $SRC_DIR/unitTest/$module/_initData.coffee
# fi
# for dir in $SRC_DIR/unitTestJs; do
#   echo $dir
# done

# cat > $SRC_DIR/unitTestJs/msgString.coffee << EOF
# msgString = require '../lib/msgStrings'
# exports.MSG_STRINGS = MSG_STRINGS = msgString.newInstance()
# EOF
# cat $SRC_DIR/apps/80_sites/01_lib/00_msgString.coffee >> $SRC_DIR/unitTestJs/msgString.coffee
# coffee -o $SRC_DIR/unitTestJs/ -b -c $SRC_DIR/unitTestJs/msgString.coffee

for dir in $SRC_DIR/unitTest/*; do
  if [ -d "$dir" ]; then
    # echo $dir
    folder=$(basename $dir)
    # echo $nm
    for filename in "${dir}"/*; do
      # echo $filename
      nm1=$(basename $filename)
      # echo nm1=$nm1
      if [[ $nm1 == fixtures ]]; then
        echo copy fixtures to: "cp -rf $SRC_DIR/unitTest/$folder/fixtures $SRC_DIR/unitTestJs/$folder/"
        # echo cp -rf $SRC_DIR/unitTest/$folder/fixtures $SRC_DIR/unitTestJs/$folder/
        mkdir -p $SRC_DIR/unitTestJs/$folder/
        cp -rf $SRC_DIR/unitTest/$folder/fixtures $SRC_DIR/unitTestJs/$folder/
      elif [[ $nm1 != _* ]]; then
        echo complie coffee to js: $nm1
        coffee -o $SRC_DIR/unitTestJs/$folder -b -c $filename
      else
        echo "do not compile file: $filename"
      fi
    done
  fi
done


# mongorestore -u  -d --drop $SRC_DIR/unitTest/dump
if [ "$report" == "Y" ]; then
  #this line is used when report needed.
  #根据参数选择是否输出report
  echo generate report
  npx nyc --reporter=html mocha  --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs" --reporter dot > $SRC_DIR/unitTest/report.txt
else
  echo 
  # single file
  if [[ "$file" ]]; then
    echo testing file $file
    #npx nyc --all --include ["built/**","unitTestJs/**"] --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs/$module/*"
    #mocha $SRC_DIR/unitTestJs/$module/*.js
    npx nyc --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs/$file"
  # from csv file as files to pass
  elif [[ "$fileArray" ]]; then
    echo testing files from csv: $fileArray
    if [[ "$fcsv" ]]; then
      echo "fcsv: $fcsv"
      mochaReportsFile=mochaReportsFile
    elif [[ "$mcsv" ]]; then
      echo "mcsv: $mcsv"
      mochaReportsFile=mochaReportsModule
    else
      echo "no csv file"
      mochaReportsFile=mochaReports
    fi

    # set dif output dir
    echo "isElastic: $isElastic"
    if [ "$isElastic" = "Y" ]; then
      outputDir="$CFG_DIR/reports/unitTestES"
    else
      outputDir="$CFG_DIR/reports/unitTest"
    fi
    echo "Output directory is set to: $outputDir"

    # NOTE: change json to spec for admin to view, currently not support multiple reporter
    # npx nyc mocha --reporter json --reporter-option output=$CFG_DIR/reports/$mochaReportsFile.json --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive $fileArray >> $CFG_DIR/logs/report.txt
    echo "npx nyc mocha --reporter json --reporter-option output=$outputDir/$mochaReportsFile.json --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive $fileArray >> $CFG_DIR/logs/report.txt"
    npx nyc mocha --reporter json --reporter-option output=$outputDir/$mochaReportsFile.json --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive $fileArray >> $CFG_DIR/logs/report.txt
    # NOTE: mochawesome is not working, due to multiple require files conflict causes not register?
    # npx nyc mocha --reporter mochawesome --reporter-options reportDir="$CFG_DIR/reports" --require mochawesome/register --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive $fileArray > $CFG_DIR/logs/report.txt
    # mocha --reporter mochawesome --reporter-options reportDir="$CFG_DIR/reports" --require mochawesome/register --recursive $fileArray > $CFG_DIR/logs/report.txt

    # npx nyc report --reporter html --reporter text --reporter json-summary --report-dir $CFG_DIR/reports
  # single dir
  elif [[ "$module" ]]; then
    echo testing module $module
    #npx nyc --all --include ["built/**","unitTestJs/**"] --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs/$module/*"
    #mocha $SRC_DIR/unitTestJs/$module/*.js
    npx nyc --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs/$module/*"

  else
    echo testing all
    echo "npx nyc --reporter=html mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs""

    npx nyc --reporter=html  mocha --require $SRC_DIR/unitTestJs/00_common/startServer.js --recursive "$SRC_DIR/unitTestJs"
    #mocha $SRC_DIR/unitTestJs/*/*.js
  fi
fi

#sendmail attach report

# str="$SRC_DIR/node_modules/.bin/istanbul report"
# eval $str


# if [ -e $SRC_DIR/unitTest/$module/clearData.coffee ]; then
#   coffee $SRC_DIR/lib/batchBase.coffee $SRC_DIR/unitTest/$module/clearData.coffee
# fi
