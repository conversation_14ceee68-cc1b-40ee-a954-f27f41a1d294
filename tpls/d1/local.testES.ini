[contact]
trustedAssignEmail = '<EMAIL>'
alertSMS = '01234556'
alertEmail = '<EMAIL>'
defaultReciveEmail =''
projectFubEmail = '<EMAIL>'

[importNotify]
bcreTo = ["<EMAIL>"]
defaultTo = ["<EMAIL>"]

[dbs]
verbose = 1

[dbs.chome]
uri="**********************************************************************************************************************************************************************************************************************************************************************************************************************************************"


[dbs.rni]
uri="************************************************************************************************************************************************************************************************************************************************"


[dbs.tmp]
uri="************************************************************************************************************************************************************************************************************************************************"


[dbs.vow]
uri="*************************************************************************************************************************************************************************************************************************************************************************************************************************************************"


[elastic]
cert = "/opt/testfs/elastic/elasticsearch-8.12.2/config/certs/http_ca.crt"
host = "https://localhost:9201"
password = "KKLZgCHvJV0Yls9wSY8L"
verbose = 2

[mailEngine]
mailEngine = "mockmail"

[mailEngine.gmail]
defaultEmail = ""

[mailEngine.gmail.auth]
pass = "dummy"
user = "<EMAIL>"

[mailEngine.mockMail]
mock = false
verbose = 3

[mailEngine.sendmail]
defaultEmail = ""

[mailEngine.sesH]
defaultEmail = ""

[mailEngine.sesL]
defaultEmail = ""

[user_file]
wwwbase = "fs_t.realmaster.c"

[propertiesBase]
useSearchEngine = "ES"

[limitAccessRate]
AccessPerSessionLimit = 9_900
AvgAccessPerSessionIP = 920
SessionPerIPThreshold = 9100

[server]
port = 8_098

[serverBase]
appHostUrl=''
canonicalHost=''
verbose = 1
wwwDomain = "www.test:8080"
srcPath = "../appweb/src"

[static]
verbose = 2

[share]
host = "http://www.test:8080"
hostNameCn = "realexpert.cn"
secret = "547dc488a8d7d5828d34437872064a9b"

[azure]
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"