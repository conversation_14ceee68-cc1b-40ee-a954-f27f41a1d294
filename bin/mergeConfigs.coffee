fs = require 'fs'
toml = require 'toml'

# deep copy of the config to avoid modifying the original, and NaN values
module.exports.deepClone = deepClone = (obj) ->
  if typeof obj isnt 'object' or obj is null
    return obj
  if Array.isArray(obj)
    return obj.map(deepClone)
  result = {}
  for key, value of obj
    if Object.prototype.hasOwnProperty.call(obj, key)
      if Number.isNaN(value)
        result[key] = Number.NaN
      else
        result[key] = deepClone(value)
  return result

module.exports.mergeConfigs = mergeConfigs = (config1, config2, onlyExisting = false) ->
  merge = (obj1, obj2) ->
    for key, value of obj2
      if typeof value is 'object' and not Array.isArray(value) and obj1[key]?
        obj1[key] = merge(obj1[key], value)
      else if not onlyExisting or obj1.hasOwnProperty(key)
        obj1[key] = value
    obj1
  result = deepClone(config1)
  merge(result, config2)

module.exports.mergeMultipleConfigs = mergeMultipleConfigs = (configFiles) ->
  mergedConfig = {}
  for file in configFiles
    content = fs.readFileSync(file, 'utf8')
    parsed = toml.parse(content)
    mergedConfig = mergeConfigs(mergedConfig, parsed)
  return mergedConfig

# convert merged config to array if all keys are numeric
# [importNotify]
# bcreTo = [<EMAIL>]
# to:[importNotify]
# bcreTo = [<EMAIL>]
module.exports.convertListFormat = convertListFormat = (config) ->
  convertObject = (obj) ->
    for key, value of obj
      if typeof value is 'object' and value isnt null and not Array.isArray(value)
        if Object.keys(value).length > 0 and Object.keys(value).every((k) -> /^\d+$/.test(k))
          # Convert to array if all keys are numeric and object is not empty
          obj[key] = Object.values(value)
        else
          # Recurse into nested objects
          convertObject(value)
    obj

  # Create a deep copy of the config to avoid modifying the original
  newConfig = deepClone(config)
  # Convert the copied config
  convertObject(newConfig)
  return newConfig


if require.main is module
  outputFile = process.argv[2]
  configFiles = process.argv.slice(3)
  console.log("configFiles=#{configFiles}, outputFile=#{outputFile}")
  mergedConfig = mergeMultipleConfigs(configFiles)
  mergedConfigConverted = convertListFormat(mergedConfig)
  fs.writeFileSync(outputFile, toml.stringify(mergedConfigConverted))
  console.log("Merged configuration saved to: #{outputFile}")
  # return 0
