fs = require 'fs'
toml = require 'toml'

# Function to parse the TOML file and get the value for a key
parseTOMLFile = (tomlFile, key) ->
  fs.readFile tomlFile, 'utf8', (err, data) ->
    if err
      console.error "Error reading file: #{err.message}"
      process.exit 1

    try
      config = toml.parse(data)
    catch e
      console.error "Error parsing TOML file: #{e.message}"
      process.exit 1

    # Retrieve the value based on the key
    keys = key.split('.')
    value = config
    for k in keys
      if value[k]?
        value = value[k]
      else
        # console.error "Key not found: #{key}"
        # process.exit 1
        value=''

    console.log value

# Check if the script is run directly
if require.main is module
  # Read command-line arguments
  if process.argv.length < 4
    console.error 'Usage: coffee getValueFromToml.coffee <toml_file> <key>'
    process.exit 1

  tomlFile = process.argv[2]
  key = process.argv[3]

  # Parse the TOML file
  parseTOMLFile tomlFile, key
