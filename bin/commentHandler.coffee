parseLine = (line) ->
  trimmedLine = line.trim()
  if trimmedLine.startsWith('#')
    return { line, content: '', comment: trimmedLine, isFullLineComment: true }
  [_, content, comment] = trimmedLine.match(/(.*?)(?:\s*#\s*(.*))?$/) or [null, trimmedLine, null]
  { line, content: content.trim(), comment, isFullLineComment: false }

parseSection = (content) ->
  content.match(/^\[([^\]]+)\]/)?[1]

parseKey = (content) ->
  content.match(/^(\w+)\s*=/)?[1]

module.exports.extractComments = (inputContent) ->
  lines = inputContent.split('\n')
  comments = {}
  outsideComments = []
  currentSection = null
  insideSection = false

  for line in lines
    { content, comment, isFullLineComment } = parseLine(line)
    
    section = parseSection(content)
    if section
      currentSection = section
      insideSection = true
      if comment
        comments[currentSection] = { inlineComment: comment }
      continue

    if isFullLineComment
      if insideSection
        comments[currentSection] = comments[currentSection] or {}
        comments[currentSection].fullLineComments = comments[currentSection].fullLineComments or []
        comments[currentSection].fullLineComments.push(comment)
      else
        outsideComments.push(comment)
      continue

    key = parseKey(content)
    if key and insideSection
      fullKey = "#{currentSection}.#{key}"
      if comment
        comments[fullKey] = { inlineComment: comment }

  [inputContent, comments, outsideComments]

module.exports.reinsertComments = (content, comments, outsideComments) ->
  lines = content.split('\n')
  newContent = []
  currentSection = null

  for line in lines
    { line: originalLine, content } = parseLine(line)
    
    section = parseSection(content)
    if section
      currentSection = section
      storedComment = comments[currentSection]?.inlineComment
      newLine = if storedComment then "#{originalLine} # #{storedComment}" else originalLine
      newContent.push(newLine)
      
      if comments[currentSection]?.fullLineComments
        newContent.push(comment) for comment in comments[currentSection].fullLineComments
      
      continue

    key = parseKey(content)
    if key and currentSection
      fullKey = "#{currentSection}.#{key}"
      storedComment = comments[fullKey]?.inlineComment
      newLine = if storedComment then "#{originalLine} # #{storedComment}" else originalLine
      newContent.push(newLine)
    else
      newContent.push(originalLine)

  if outsideComments and outsideComments.length > 0
    newContent.push('')  # Add an empty line before outside comments
    newContent.push(comment) for comment in outsideComments

  newContent.join('\n')

