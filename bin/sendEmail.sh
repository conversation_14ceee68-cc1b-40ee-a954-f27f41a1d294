#!/bin/bash

rmText="$1"
RM_TO="$2"

RM_FROM=<EMAIL>
RM_URL=https://ml1.realmaster.cc/send


echo "rmText: $rmText"
echo "RM_TO: $RM_TO"

# 检查输入参数长度
if [ ${#rmText} -gt 0 ] && [ ${#RM_TO} -gt 0 ]; then
  echo "======================= Send email ======================="
  
  mail=$(jq -n \
    --arg from "$RM_FROM" \
    --arg to "$RM_TO" \
    --arg subject "Systemd service failed" \
    --arg html "$rmText" \
    '{from: $from, to: $to, subject: $subject, html: $html}')
  
  # split .to to array by ','
  newEmail=$(jq -r '.to |= split(",")' <<< "${mail}")

  curl -X POST \
    --header "Content-Type: application/json" \
    --header "X-RM-Auth: RM" \
    -s \
    --data "$newEmail" \
    $RM_URL
fi
