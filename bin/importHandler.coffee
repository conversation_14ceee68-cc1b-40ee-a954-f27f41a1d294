fs = require 'fs'
path = require 'path'
toml = require 'toml'
{mergeConfigs} = require './mergeConfigs'

# Extract @import statements from content
module.exports.getImports = getImports = (content) ->
  # Regular expression to match @import "filename"
  importRegex = /(@import.+)/g
  importStatements = []

  # Extract @import statements and remove them from content
  contentWithoutImports = content.replace(importRegex, (match, importPath) ->
    importStatements.push(importPath.trim())
    return ''  # Remove @import statement from the original file content
  )

  return [importStatements, contentWithoutImports]


# Process all import files
module.exports.processImportFiles = (configDir) ->
  configFiles = fs.readdirSync(configDir)
  processedConfigs = {}

  for file in configFiles
    filePath = path.join(configDir, file)
    # console.log('Processing file:', filePath)
    if fs.statSync(filePath).isFile() and file.endsWith('.ini')
      content = fs.readFileSync(filePath, 'utf8')
      # Handle imports
      solvedImport = handleImports(content, configDir)
      solvedImportJson = toml.parse(solvedImport)
      # console.log('solvedImportJson:', solvedImportJson)
      processedConfigs[file] = solvedImportJson

  # console.log('processedConfigs:', processedConfigs)
  return processedConfigs


# Function to handle @import statements
module.exports.handleImports = handleImports = (content, configDir, imports = {}) ->
  [importStatements, contentWithoutImports] = getImports(content)
  # Ensure importStatements is always an array
  importStatements = importStatements or []
  # Parse the main content after removing import statements
  contentWithoutImportsJson = toml.parse(contentWithoutImports)


  # Process each import statement
  for importStatement in importStatements
    importPath = importStatement.replace(/^@import\s+/, '').trim()
    importFilePath = path.join(configDir, importPath)

    # If the file has already been processed and cached, skip re-import
    if imports[importPath]
      # console.log('in imports:')
      continue # Skip this import as it's already processed

    # Otherwise, process the import file
    if fs.existsSync(importFilePath)
      # console.log('not in imports:')
      importContent = fs.readFileSync(importFilePath, 'utf8')
      # Recursively handle imports
      importContentWithImports = importContent
      importContentWithImports = handleImports(importContent, configDir, imports)
      # imports[importPath] = importContentWithImports
      importedConfigJson = toml.parse(importContentWithImports)
      contentWithoutImportsJson = mergeConfigs(importedConfigJson, contentWithoutImportsJson)
    else
      throw new Error("Import file not found: #{importPath}")

  return toml.stringify(contentWithoutImportsJson)

