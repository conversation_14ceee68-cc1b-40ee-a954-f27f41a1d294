[equifax]
clientId = ""
clientSecret = ""
col = "equifax"
db = "vow"

  [equifax.api]
  report = "https://api.uat.equifax.ca/inquiry/1.0/sts"
  scope = "https://api.equifax.ca/inquiry/1.0/sts"
  token = "https://api.uat.equifax.ca/v2/oauth/token"

  [equifax.customer]
  customerCode = ""
  customerId = "for billing get from Clients"
  customerNumber = ""
  customerReferenceNumber = ""
  securityCode = ""
