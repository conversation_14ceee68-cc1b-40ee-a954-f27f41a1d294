[emailTemplate]
path = "${srcPath}/emailTemplate"

[mailEngine]
mailEngine = "SES"
mailEngine2 = "rmMail"

  [mailEngine.gmail]
  defaultEmail = "<EMAIL>"

    [mailEngine.gmail.auth]
    pass = "RealMasterCOM***"
    user = "<EMAIL>"

  [mailEngine.mockMail]
  mock = false
  verbose = 1

  [mailEngine.rmMail]
  defaultEmail = "<EMAIL>"
  url = "https://ml1.realmaster.cc/send"

  [mailEngine.sendGrid]
  apiKey = "*********************************************************************"
  fromEmail = "<EMAIL>"
  wpBackEndDomain = "https://www.realmaster.cn/wpheaderfooter/"

  [mailEngine.sendmail]
  defaultEmail = "<EMAIL>"

  [mailEngine.ses]
  accessKeyId = "********************"
  defaultEmail = "<EMAIL>"
  region = "us-east-1"
  secretAccessKey = "Uogp6eMqmbIhxB43mqCYPJYBT9x3vnQZwrawvz6W"
  url = "http://934.230.229.12:8088/ses.php1"

  [mailEngine.sesH]
  accessKeyId = ""
  defaultEmail = "<EMAIL>"
  region = "us-east-2"
  secretAccessKey = ""

  [mailEngine.sesL]
  accessKeyId = ""
  defaultEmail = "<EMAIL>"
  region = "us-east-1"
  secretAccessKey = ""

  [mailEngine.smtp]
  service = "Gmail"

[mailEngineList.0]
accessKeyId = "********************"
email = "<EMAIL>"
engine = "SES"
region = "us-east-2"
secretAccessKey = "NqYJDdVhIMdvVqlJnmBSIBsbHKCEQLlkw91/v4rA"
url = "http://934.230.229.12:8088/ses.php1"

[mailEngineList.1]
email = "<EMAIL>"
engine = "rmMail"
url = "https://ml1.realmaster.cc/send"
