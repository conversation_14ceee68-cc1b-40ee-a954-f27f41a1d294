[dbs]
mongo4 = true
v36 = true
verbose = 3

[preDefColls.emailMXFailed]
collName = "email_mx_failed"
dbName = "chome"

[preDefColls.emailWhitelist]
collName = "email_whitelist"
dbName = "chome"

[preDefColls.geoStat]
collName = "geo_stat"
dbName = "vow"

  [preDefColls.geoStat.options]
  expireAfterSeconds = 31_536_000 # 365 * 24 * 3600

    [preDefColls.geoStat.options.timeseries]
    metaField = "metadata"
    timeField = "ts"

[preDefColls.mailLog]
collName = "mail_log_priority"
dbName = "chome"

  [preDefColls.mailLog.options]
  expireAfterSeconds = 2_592_000 # 30 * 24 * 3600

    [preDefColls.mailLog.options.timeseries]
    metaField = "engine"
    timeField = "timestamp"

[preDefColls.uuid]
collName = "user_uuid"
dbName = "chome"
