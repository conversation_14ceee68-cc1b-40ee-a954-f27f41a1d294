[crime]
v3ImpAuth = "3c4d837c25cf027b763664bd20479344"

[iShare]
secret = "ojF7llUbqMJTHxQLr75a6IrPtsnR6DyI"
wwwbase = "http://www.isharing.co/api/realmaster"

[isharing]
ips = [ "**************", "***************" ]

[p58]
host = "autoinfo.gm.58.com" # 'autoinfo.58v5.cn'
protocol = "http"
wbcid = "39141494639361" # 58
wbcsec = "xMPUNwIZFJrvTK08t0ak"

[wechat]
AESKey = ""
appId = "wxf43abc63fd6c1b19"
appName = "RealMaster"
domain = "realmaster.cn"
secret = "fcd6d3800bb2906bacbee676685a0de4"
token = "" # 微信消息推送接口校验

[wechatApp]
AppID = "wxcf33ce325754da25"
AppSecret = "69a59252b9f587356b70706d4080d2ae"
domain = "https://realmaster.com/"

[wechatWeb]
AppID = "wxd1989971aaef81e7"
AppSecret = "0e3783110749989da5c93b170d04609a"

[wx_config.RealMaster]
appid = "wxf43abc63fd6c1b19"
secret = "fcd6d3800bb2906bacbee676685a0de4"
