[pyfpimg.imageFailure]
failure_window_seconds_get = 10
failure_window_seconds_save = 10
max_consecutive_failures_get = 5
max_consecutive_failures_save = 5

[pyfpimg.imageOutput]
floorplans_path = "/tmp/floorplans"
surveys_path = "/tmp/surveys"

[pyfpimg.imageSource]
local_path_cda = "/mnt/md0/mlsimgs/crea/ddf/img/"
local_path_creb = "/mnt/md0/mlsimgs/creb/mls/"
local_path_oreb = "/mnt/md0/mlsimgs/oreb/mls/"
local_path_trb = "/mnt/md0/mlsimgs/treb/mls/"
url_host = "img.realmaster.com"

[pyfpimg.kafka.request]
_test_topic_init = "test-fpimg-request-init"
_test_topic_routine = "test-fpimg-request-routine"
auto_offset_reset = "earliest"
bootstrap_servers = "127.0.0.1:9092"
group_id = "my-group"
timeout = 60
topic_init = "fpimg-request-init"
topic_routine = "fpimg-request-routine"
worker_num = 4

[pyfpimg.kafka.response]
_test_topic_name = "test-fpimg-response"
bootstrap_servers = "127.0.0.1:9092"
topic_name = "fpimg-response"

[pyfpimg.log]
file = "./logs/pyfpimg.log"
level = "DEBUG"

[pyfpimg.model]
name = "FPC_20240621_0982_k2140.h5"
path = "model"

[pyfpimg.mongo]
collection = "floorplan_classification"
host = "127.0.0.1"
name = "listing"
password = "d1"
port = 27_017
tlsCAFile = "/etc/mongo/ca.crt"
user = "d1"
