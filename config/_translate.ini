[azure]
endpoint = "https://api.cognitive.microsofttranslator.com"
region = "eastus"
subscriptionKey = "e678a9f6c8764711849e0f17504aa696"

[claude]
endpoint = "https://api.anthropic.com/v1/messages"
key = "sk-"

[deepL]
endpoint = "https://api.deepl.com/v2/translate"
key = "c8311202-a251-97d9-b15d-3795ac88e820"

[deepseek]
endpoint = "https://api.deepseek.com/chat/completions"
key = "***********************************"

[gemini]
key = "AIzaSyBHoyzQTkMF_Aps9ykYZ00BRHdtfGc_TRk"

[grok]
endpoint = "https://api.x.ai/v1/chat/completions"
key = ""

[i18n]
allowed = null
transFiles = "${srcPath}/transfiles"

  [i18n.abbr]
  file = "${srcPath}/abbr.csv"

    [i18n.abbr.coll]
    coll = "abbr"
    db = "chome"

  [i18n.cityColl]
  coll = "i18nCity"
  db = "chome"

  [i18n.coll]
  coll = "i18n"
  db = "chome"

[openAI]
endpoint = "https://api.openai.com/v1/chat/completions"
key = "********************************************************************************************************************************************************************"
