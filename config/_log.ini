[log]
console = true
format = ":date2 :remote-addr :method :host:url :start-time :status :content-type :content-length :response-time"
path = "${configPath}/logs"
sformat = ":date2 :remote-addr :method :host:url :start-time :user-agent :referrer"
verbose = 1

  [log.buffer]
  duration = 1_000 # ms
  size = 10

  [log.mongodb]
  db = "chome"
  error = "log_error"
  notfound = "log_notfound"

  [log.requests]
  interval = 5_000 # ms
  level = "warn"
  limit = 10
  timeout = 100_000 # ms developer 5s, product 100s
