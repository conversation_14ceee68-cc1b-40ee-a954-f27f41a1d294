## 上线说明

### Step 0 - get code and install package
- 获取 realmaster-appweb repo，refactor_cfglocal branch代码。
- 获取 rmconfig repo，refactor_cfglocal branch代码。设置srcPath in local.ini (Default path is at the same level as rmconfig)。
  ```
  ├── rmconfig
  │   ├── built # content generated from config -> built
  │   ├── keys # key/pem file for services
  │   ├── logs # server/batch logs
  │   └── config # basic config files
  ├── realmaster-appweb # [see:](realmaster-appweb/src/readMe.md)
  │   ├── batch
  │   ├── docs
  │   ├── extra
  │   ├── fileStorage
  │   ├── n5
  │   └── src
  ├── pyfpimg
  ├── pyml
  ```
- Install the packages in realmaster-appweb/src，rmconfig/
  `npm install`


### Step 1 - Generate local file

- appweb：根据服务器上原来的`cfglocal/built/config.coffee`，`cfglocal/built/vhost.yml`文件，及`rmconfig/tpls/d1/local.ini`文件，编写需要使用的`rmconfig/local.ini`文件。（做好`rmconfig/local.ini`文件后，可以运行`coffee config.coffee`生成文件`built/app_web.ini`，再次比较确认配置的正确）
<br>
- unit test：根据服务器上原来的`cfglocalTest/built/config4.coffee`文件，及`rmconfig/tpls/d1/local.test.ini`文件，编写需要使用的`rmconfig/local.test.ini`文件。（做好`rmconfig/local.test.ini`文件后，可以运行`coffee config.coffee`生成文件`built/unit_test.ini`，再次比较确认配置的正确）
<br>
- unit test ES：根据服务器上原来的`cfglocalTestEs/built/config4.coffee`文件，及`rmconfig/tpls/d1/local.testES.ini`文件，编写需要使用的`rmconfig/local.testES.ini`文件。（做好`rmconfig/local.testES.ini`文件后，可以运行`coffee config.coffee`生成文件`built/unit_testES.ini`，再次比较确认配置的正确）



### Step 2 - unit test
- 在rmconfig目录下运行unit test，确保全部通过，
  通过`./logs/unit_test.log`查看log，`systemctl --user status unit_test`查看状态，reports查看结果。通过添加-cron参数开启timer
  ```
  sh start.sh -t unit_test [-e <EMAIL>,<EMAIL>] [-cron '*-*-* 09:40:00']
  ```
- 在rmconfig目录下运行unit test ES，确保全部通过
  通过`./logs/unit_testES.log`查看log，`systemctl --user status unit_testES`查看状态，reports查看结果。通过添加-cron参数开启timer
  ```
  sh start.sh -t unit_testES [-e <EMAIL>,<EMAIL>] [-cron '*-*-* 09:40:00']
  ```

- 如果需要自启动，可以按照运行时的提示，使用root放入/etc，并启动。
  ```
   # sh ./start.sh -t unit_test -w 'multi-user.target' [-cron '*-*-* 09:40:00'] to get info and do some cmd like:
  sudo cp ~/rmconfig/built/etc/systemd/unit_test.service /etc/systemd/system/
  sudo cp ~/rmconfig/built/etc/systemd/unit_test.timer /etc/systemd/system/
  sudo systemctl enable unit_test.timer


  # sh ./start.sh -t unit_testES -w 'multi-user.target'  [-cron '*-*-* 09:40:00']to get info and do some cmd like::
  sudo cp ~/rmconfig/built/etc/systemd/unit_testES.service /etc/systemd/system/
  sudo cp ~/rmconfig/built/etc/systemd/unit_testES.timer /etc/systemd/system/
  sudo systemctl enable unit_testES.timer
  ```
  同时为了在任务失败时候能够发送邮件，需要按照运行时的提示执行：
  ```
  sudo cp ~/rmconfig/built/etc/systemd/send_email_on_failure@.service /etc/systemd/system/
  ```



### Step 3 - appweb
- 运行以下命令，生成对应的service文件，并按照提示，运行appweb。
通过`./logs/appweb_camte.log`查看log，`systemctl --user status app_web`查看状态，
确保服务正常启动。
待完全正常运行后，停掉原来正运行中的appweb
  ```
  sh start.sh -w 'multi-user.target'

  then start the service according to the log, like:
  Please use sudo run:
  sudo cp ~/rmconfig/built/etc/systemd/appweb.service /etc/systemd/system/
  sudo systemctl enable appweb
  sudo systemctl start appweb

  ```


### Step 4 - batch
- 停掉原来正运行中的batch，运行以下命令，生成对应的service文件，并按照提示，运行appweb。确保每个输入对应的`-n name`
  ```
    # batch has to use -n option to specify the name of the batch
    sh start.sh -t batch -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun" -w 'multi-user.target'
  ```



###  后续注意
- appweb开发中，使用`config = CONFIG(['serverBase','mailEngine'])`只调用需要的config section


###   遗留问题
- 单元测试有时候会不通过：
  ```
      {
        "title": "import from treb master",
        "fullTitle": "saveToMaster addLog to PropertiesImportLog import from treb master",
        "file": "/opt/testfs/maggie/realmaster-appweb/src/unitTestJs/libapp/saveToMaster.js",
        "duration": 13,
        "currentRetry": 0,
        "err": {
          "message": "expected null to exist",
          "stack": "AssertionError: expected null to exist\n    at /opt/testfs/maggie/realmaster-appweb/src/unitTestJs/libapp/saveToMaster.js:19:159\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n"
        }
      }
      {
      "title": "\"before all\" hook in \"CMA\"",
      "fullTitle": "CMA \"before all\" hook in \"CMA\"",
      "file": "/home/<USER>/realmaster-appweb/src/unitTestJs/cma/cma.js",
      "duration": 1165120,
      "currentRetry": 0,
      "err": {
        "stack": "Error: Timeout of 30000ms exceeded. For async tests and hooks, ensure \"done()\" is called; if returning a Promise>
        "message": "Timeout of 30000ms exceeded. For async tests and hooks, ensure \"done()\" is called; if returning a Promise, ens>
        "code": "ERR_MOCHA_TIMEOUT",
        "timeout": 30000,
        "file": "/home/<USER>/realmaster-appweb/src/unitTestJs/cma/cma.js"
      }
    }
  ```   
- 整合pyml 