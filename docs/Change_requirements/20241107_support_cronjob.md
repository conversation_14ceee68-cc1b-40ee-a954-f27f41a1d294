# 需求 [support_cronjb]

## 反馈
1. 之前的cronjob需要使用timer运行
2. sendInRealStatus.sh =
/home/<USER>/rmcfg/start.sh lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "1 month ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL>> /home/<USER>/rmcfg/logs/exportInRealGroupStats.log 2>&1 & 命令不支持直接运行start.sh


## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2024-11-07

## 原因

1. 之前没有考虑到有很多cronjob需要运行
2. sendInRealStatus.sh不能运行是因为原来start.sh 本身有-e参数，没有设置，后面batch又使用了-e参数，导致参数解析错误。


## 解决办法

1. 添加-cron '*-*-* 09:40:00' 参数，接收OnCalendar格式，如果添加了这个参数，会自动生成对应的timer文件，并启动。
2. 原来的unit_test_timer,unit_testES_timer 也合并到 unit_test/unit_testES -cron '*-*-* 09:40:00'中。
3. 将整个batch的参数，包含在 -cmd ""中，确保和start.sh参数分开。




## 确认日期:    2024-11-08

## online-step

1. 拉取最新代码，sh start.sh 运行appweb。
2. 重启watch ca1 + ca3 已经运行的batch，添加-cmd命令。
3. cronjob 按照general.md中New Config 命令运行。
4. 使用这个命令，确认cronjob：`systemctl list-timers`。

 
