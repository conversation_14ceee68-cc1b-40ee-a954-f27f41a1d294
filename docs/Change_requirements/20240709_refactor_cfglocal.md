# 需求 [refactor cfglocal]

## 反馈
1. cfglocal 配置不兼容python
2. cfglocal 不同人的修改无法merge
3. 不应该必须配置所有东西才能运行起来，而是根据实际情况配置
4. 未来这个配置会增多，不会减少


## 需求提出人:    Rain, Fred

## 修改人:       Maggie

## 提出日期

1. 2024-07-09

## 需求确认日期

1. 2024-07-17

## 原因
现在的配置设计过于简单，随着增加需求，目前配置无法满足，且不同人的修改无法merge。



## 解决办法
1. 拆分出小功能配置文件 config文件夹下
  [如_server.ini, _push_notification.ini, _third_party.ini,_geo_keys.ini]
  _geo_keys.ini:
  ```
  [mapbox]
  web = "pk.eyJ1IjoicmVhbG"
  app = "pk.eyJ1IjoicmVhbG"
  housemax = "pk.eyJ1IjoicmVhbG"

  [google]
  geocoderServer = "AIzaSy"

  ```

2. 利用小功能配置文件制作大功能配置文件，config文件夹下
  当多个文件中出现相同session，将里面文件合并，
  如果遇到相同的，使用新的配置覆盖旧的，如[mapbox] web最终为'AAA'
  geocode.ini:
   ```
   @import _geo_keys    #默认使用config文件夹下
   @import _chome_db
   @import _vow_db

   [mapbox]
   web = 'AAA'
   tmp = 'tmp'
   ```

3. 每个batch作为一个大功能配置文件，并引用_batch_base.ini

2. 每个用户有一个自己的.env.ini文件，里面写入个性化的配置，这部分配置会覆盖掉默认配置
   defaultEmail = 'email'

3. config.coffee
   1. 将config下所有文件按照字母排序后覆盖原文件。【所有人提交代码前必须运行setup.coffee获得排序后的config文件再提交】
   2. 将config文件夹中所有ini生成对应的built/文件， 同时会处理@import。
   3. 运行后都重新build，不管之前是否有built
   4. 提供envonly选项 --- 合并到start.sh/直接拿到路径
   5. 将相对路径转换成绝对路径

4. 根据[toml wiki](https://github.com/toml-lang/toml/wiki) 查找所有js包，
   选取下载数量最多的[toml](https://www.npmjs.com/package/toml)，可以将toml转化成json。找到下载量更高的[@iarna/toml](https://www.npmjs.com/package/@iarna/toml)，避免了toml的object with null prototype to normal object问题，且提供json->toml和toml->json。

   修改appweb中需要parse_config 部分，判断文件格式，支持toml文件
   需要修改的文件如下，只修改读取config部分，程序用法不变，
    coffeemate3.coffee
    appBase.coffee
    /src/unitTest/00_common/helpers.coffee

5. config文件名称，使用下划线，文件内容使用camelCase  

6. 不要原来config中的app_config，对应scr config【如appConfig = config.app_config，55个文件】都需要修改。
   原来的config = CONFIG(true)，true会导入所有config[在运行start.sh时候导入的所有ini]
   同时支持接收section名称，如config = CONFIG('mapbox') 

7. 修改start.sh添加接收-c参数运行cmate3,
    如果没有设置，使用默认built/app_web.ini, batch等还按照原来方式运行:
    ./start.sh -c built/geocode.ini -cmd "lib/batchBase.coffee batch/prop/floorPlanClassification.coffee init preload-model debug" 
    如果需要多个config文件，./start.sh -c built/config.ini -c built/config1.ini -c built/config2.ini -cmd "lib/batchBase.coffee ...
    在start.sh 中会将多个ini文件合并排序，并写入文件built/config_{pid}.ini， 同时复制到built/run/config.ini 用于systemctl运行

8. start.sh 使用
   1. 运行`sh start.sh`，会直接启动systemd。
      目前以下步骤已写在 start.sh里面, 同时支持自己运行 `systemctl start`：
      a. 将运行文件copy到home下
         ```
         ~/.config/systemd/user/
         sudo cp built/systemd/appweb.service /etc/systemd/user
         sudo cp built/systemd/batch.service /etc/systemd/user
         ```
      b. 运行systemctl --user daemon-reload；systemctl --user start appweb.service




使用步骤：
0. 确认已经下载realmaster-appweb
   ```
      RealMaster/
      │
      ├── realmaster-appweb/
      │   ├── src/
      │
      ├── rmconfig/
      │   ├── config
   ```

1. 按照需要填写.env.ini文件，该文件内的信息在生成的built文件中，会覆盖原来的值。可以设置个人配置，如alertEmail等，使用toml格式：
   ```
   alertEmail = '<EMAIL>'
   ```

2. 安装包 `npm install`
   
3. 运行coffee config.coffee，对config下文件进行排序，并生成built下文件。
   
4. 服务器上运行`sh start.sh`启动systemd服务。
5. 开发人员在自己mac上运行，需要使用orbStack[具体设置见README]
   1. 命令行brew install orbstack 安装软件
   2. 图形化客户端machine + rocky version9，或者按照[文档](https://docs.orbstack.dev/machines/)根据自己系统，使用命令安装
   3. 命令行运行 `ssh orb` 进入rocky
   4. 安装node, coffee, mongosh
   5. 编写需要的文件如：`local.ini`，`local.test.ini`，`local.testes.ini`
   6. 在orb中运行`sh start.sh`


6. 通过systemctl 相关命令查看log，status：
   ```
   systemctl --user start appweb.service
   systemctl --user status appweb.service
   systemctl --user stop appweb.service
   journalctl --user -u appweb.service

   # 重新加载
   systemctl --user daemon-reload
   systemctl --user restart appweb.service

   #log:
   ~/appweb_log_file
   ~/batch_log_file

   ```
