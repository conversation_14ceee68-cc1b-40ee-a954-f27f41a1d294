# 需求 [improve1]

## 反馈
1. 支持batch 命令行运行；
2. batch，可以指定不自动重启
3. 删除systemd


## 需求提出人:   Fred, Rain

## 修改人：      Maggie

## 提出日期:     2024-12-31

## 原因
1. 只运行一次的batch， 需要提供在命令行运行
2. 需要提供自动重启选项
3. 需要提供删除systemd选项


## 解决办法
1. 当运行start.sh 时，不输入 -t batch 时, 直接在命令行运行 `./start.sh -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun debug"`
2. 默认不自动重启，添加[-rs]  restart参数时候 才自动重启`./start.sh -n fix_floorplanId -cmd "lib/batchBase.coffee batch/condos/fix_floorplanId.coffee dryrun debug" -rs`
3. 添加 -delete 参数，删除对应的systemd，需要输入-t 和 -n `./start.sh -t batch -n fix_floorplanId  -delete`
4. 添加 -del_failed 参数，删除所有systemd failed的service `./start.sh -del_failed`



## 确认日期:    2025-01

## online-step
拉取最新代码

 
