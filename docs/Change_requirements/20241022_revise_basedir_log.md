# 需求 [revise_basedir_log]

## 反馈
1. 需要根据生成环境的config，添加对应的config字段
2. base_dir，crea_base_dir，bcre_base_dir 需要从config读取，不能hardcode
3. log dir也要可以设置
4. 需要支持直接运行service文件，直接将batch需要的参数写入service文件


## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2024-10-23

## 原因

1. 之前只根据测试服的config生成的对应config字段，缺少生成服的字段
2. 之前以为base_dir等都是固定的，直接写在了`config.coffee`中。


## 解决办法

1. 对比生产服的config和目前已有的config，补充新字段:
```
[contact.failBackEmail]
0 = "<EMAIL>"

[rni]
onDMergeDateSpanForSameSid = 1728000000 # 20 * 24 * 3600000
onDMergeDateSpanForDiffSid = 432000000 # 5 * 24 * 3600000

[wechat]
token = "" # 微信消息推送接口校验
AESKey = ""

[mailEngine.sendGrid]
wpBackEndDomain = "https://www.realmaster.cn/wpheaderfooter/"

[mapbox]
address = ""


[abTest]
ratioOfA = 0.5

[serverBase]
bcreBaseDir = "/opt/data/bcre"
creaBaseDir = "/mnt/mls/sync/crea"
forceHTTPS = false
trebBaseDir = "../treb"
```

2. 在`config.coffee`中添加读取`local.ini`文件。如果有设置对应值，则使用config.serverBas中trebBaseDir,creaBaseDir,bcreBaseDir，如果没有则使用默认值`/tmp`
3. 在systemed文件中，使用`StandardOutput=append:${LOG_PATH}/appweb_cmate.log`，并在`config.coffee`中获取config.log.path，替换systemed文件。
4. 整体运行同原来保持一致：`sh start.sh` 生成需要的service，普通用户会自动运行systemd。需要后续自启动的，会生成对应的需要操作步骤，不自动运行systemd。
5. 对于普通使用，systemd service文件内不添加User，Group。对于需要开机启动的，service文件内添加User，Group，并生成到./built/etc/ 下面，同时print出后面需要的步骤(unit test 同时需要根据提示cp timer文件)
    ```
    sudo cp ./built/etc/appweb.service /etc/systemd/system/
    sudo systemctl enable appweb.service
    sudo systemctl start appweb.service
    ```
6. 调整built下目录结果：
    ```
    ├── built
    │   ├── config  # generated default config ini files
    │   ├── systemd
    │   ├── rmIni  # config ini files used in service file
    │   └── etc
    │       ├── systemd
    │       ├── rmIni

    ```
7. geoip 放到geoCoderService中
8. config.coffee 添加支持单个文件排序，如coffee config.coffee -c local.ini
9. allowedIPs 修改为list `allowedIPs = ["127.0.0.1"]`
  


## 确认日期:    2024-10-23

## online-step

1. 拉取最新代码，sh start.sh 运行
 
