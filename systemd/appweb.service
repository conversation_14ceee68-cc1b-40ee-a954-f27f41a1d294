[Unit]
Description=appweb service
StartLimitIntervalSec=30
StartLimitBurst=3
OnFailure=send_email_on_failure@%n.service

[Service]
${ENV}
ExecStart=${EXEC_START}
ExecReload=/bin/bash -c 'pid=$(pgrep -u $USER -f "node.*coffee.*cmate.*/*.ini" | head -n 1); echo "$pid" ;kill -s USR2 $pid'
ExecStop=/bin/bash -c 'mv "$BUILT_DIR/rmIni/config_${MAINPID}.ini" "$BUILT_DIR/rmIni/config_${MAINPID}.end.ini"'
# User=${USER}
# Group=${GROUP}
${USER_GROUP}
KillMode=process
TimeoutStopSec=10
StandardOutput=append:${LOG_PATH}/appweb_cmate.log
Restart=${RESTART_SERVICE}
SuccessExitStatus=0 143

[Install]
# WantedBy=default.target/multi-user.target
WantedBy=${WANTED_BY}