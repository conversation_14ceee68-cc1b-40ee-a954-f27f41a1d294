[Unit]
Description=pyfpimg service
StartLimitIntervalSec=30
StartLimitBurst=3
OnFailure=send_email_on_failure@%n.service

[Service]
${ENV}
ExecStart=${EXEC_START}
# ExecStop=/bin/bash -c  'ps -ef | grep " $MAINPID " | grep python | awk \'{print $2}\' | xargs kill && sleep 100 '
#User=
#Group=
${USER_GROUP}
KillMode=process
TimeoutStopSec=10
StandardOutput=append:${LOG_PATH}/pyfpimg.log
Restart=${RESTART_SERVICE}
RestartSec=5s
SuccessExitStatus=0 143
TimeoutStopSec=180s


[Install]
WantedBy=${WANTED_BY}